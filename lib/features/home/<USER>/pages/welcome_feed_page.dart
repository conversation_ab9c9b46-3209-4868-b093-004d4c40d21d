import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/router/app_router.gr.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/config/theme/app_assets.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/core/utils/platform_utils.dart';
import 'package:gotcha_mfg_app/features/exercise/presentation/pages/mixed_exercise_page.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/blocs/welcome_feed/welcome_feed_cubit.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/pages/home_page.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/widgets/exercise_card.dart';
import 'package:gotcha_mfg_app/shared/widgets/app_header.dart';
import 'package:gotcha_mfg_app/shared/widgets/curved_separator.dart';
import 'package:gotcha_mfg_app/shared/widgets/loading_widget.dart';
import 'package:gotcha_mfg_app/shared/widgets/retry_widget.dart';

import '../../../../core/mixpanel_service.dart';
import '../../../../core/utils/snackbar_service.dart';
import '../../../../locator.dart';
import '../../../../shared/widgets/primary_button.dart';

/// The first page the user sees when they open the app.
///
/// This page contains a list of exercises and a button to navigate to the
/// [HomePage].
@RoutePage()
class WelcomeFeedPage extends StatefulWidget {
  /// Creates a [WelcomeFeedPage].
  ///
  /// The [key] parameter is used to identify the widget.
  const WelcomeFeedPage({super.key});

  @override
  State<WelcomeFeedPage> createState() => _WelcomeFeedPageState();
}

class _WelcomeFeedPageState extends State<WelcomeFeedPage>
    with TickerProviderStateMixin {
  late AnimationController _bounceController;
  late Animation<double> _bounceAnimation;

  @override
  void initState() {
    super.initState();

    // Initialize bounce animation
    _bounceController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _bounceAnimation = Tween<double>(
      begin: 0.0,
      end: -10.0,
    ).animate(CurvedAnimation(
      parent: _bounceController,
      curve: Curves.elasticOut,
    ));

    // Start the bouncing animation and repeat it
    _bounceController.repeat(reverse: true);

    sl<MixpanelService>().trackScreenView(
      'Welcome Feed Page',
      properties: {'Code': 'screen_view.welcome_feed_page'},
    );

    context.read<WelcomeFeedCubit>().getExercises();
    WidgetsBinding.instance.addPostFrameCallback((_) => _setStatusBar());
  }

  void _setStatusBar() {
    // SystemChrome.setSystemUIOverlayStyle(
    //   const SystemUiOverlayStyle(
    //     statusBarColor: Colors.white,
    //     statusBarBrightness: Brightness.light,
    //     statusBarIconBrightness: Brightness.dark,
    //   ),
    // );
  }

  @override
  void dispose() {
    _bounceController.dispose();
    super.dispose();
  }
  @override
  Widget build(BuildContext context) {
    return BlocConsumer<WelcomeFeedCubit, WelcomeFeedState>(
      listener: (context, state) {
        if (state is WelcomeFeedError) {
          SnackBarService.error(
            context: context,
            message: state.error,
          );
        }
        if (state is WelcomeFeedLoaded) {
          if ((state.exercisesResponse.data?.exercises?.length ?? 0) < 3) {
            context.read<WelcomeFeedCubit>().getExercises();
          }
          // if (state.viewedStatusResponse?.data?.isAtLeastOneViewed == true) {
          //   context.router.replaceAll([const HomeRoute()]);
          // }
        }
      },
      builder: (context, state) {
        var textTheme = Theme.of(context).textTheme;
        if (state is WelcomeFeedLoaded) {
          return Scaffold(
            appBar: AppBar(
              toolbarHeight: 0,
              elevation: 0,
              systemOverlayStyle: const SystemUiOverlayStyle(
                statusBarColor: Colors.white,
                statusBarBrightness: Brightness.light,
                statusBarIconBrightness: Brightness.dark,
                systemNavigationBarColor: AppColors.grey,
                systemNavigationBarIconBrightness: Brightness.dark,
              ),
            ),
            backgroundColor: Colors.white,
            body: Padding(
              padding: EdgeInsets.only(
                top: isIos ? 4 : 8,
                right: 8,
                left: 8,
              ),
              child: Column(
                // Changed from ListView to Column
                children: [
                  GestureDetector(
                    child: const AppHeader(
                      title: 'Welcome',
                      subTitle: 'to the Mental Fitness Gym',
                      showLogo: true,
                    ),
                  ),
                  const CurvedSeparator(
                    outerColor: AppColors.navy,
                    innerColor: AppColors.grey,
                  ),
                  Expanded(
                    // Added Expanded to make this take remaining space
                    child: Container(
                      decoration: const BoxDecoration(
                        color: AppColors.grey,
                      ),
                      child: SingleChildScrollView(
                        // Changed from ListView to SingleChildScrollView
                        child: Column(
                          // Using Column inside SingleChildScrollView
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 24),
                              child: Text(
                                "We've picked out 3 exercise recommendations just for you. Tap one to continue:",
                                style: textTheme.ralewaySemiBold.copyWith(
                                  fontSize: 17,
                                ),
                              ),
                            ),
                            const Gap(16),
                            // Removed the Expanded widget here
                            Stack(
                              children: [
                                ListView.separated(
                                  itemCount: state.exercisesResponse.data?.exercises
                                          ?.length ??
                                      0,
                                  padding: EdgeInsets.zero,
                                  shrinkWrap: true, // This is important
                                  physics:
                                      const NeverScrollableScrollPhysics(), // This is important
                                  itemBuilder: (context, index) {
                                    var exercise = state
                                        .exercisesResponse.data?.exercises?[index];
                                    return Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 24),
                                      child: GestureDetector(
                                        onTap: () {
                                          Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                              builder: (context) =>
                                                  MixedExercisePage(
                                                notification: true,
                                                id: exercise?.exerciseId ?? '',
                                                seriesId: "",
                                                isLast: false,
                                                isFirst: false,
                                                isOverride: false,
                                              ),
                                            ),
                                          );
                                          // mixpanel
                                          sl<MixpanelService>().trackButtonClick(
                                              'Exercise Tapped',
                                              properties: {
                                                'Page': 'Welcome Feed Page',
                                                'Code':
                                                    'click.welcome_feed_page.exercise_tapped'
                                              });
                                        },
                                        child: ExerciseCard(
                                          title: exercise?.exerciseTitle ?? 'N/A',
                                          subtitle: exercise?.exerciseType ?? 'N/A',
                                          duration:
                                              exercise?.mediaDuration ?? "N/A",
                                          tag: exercise?.categoryName ?? 'N/A',
                                          thumbnail:
                                              exercise?.thumbnailUrl ?? 'N/A',
                                        ),
                                      ),
                                    );
                                  },
                                  separatorBuilder: (context, index) {
                                    return const Gap(12);
                                  },
                                ),
                                // Bouncing hand icon positioned at right bottom
                                Positioned(
                                  right: 24,
                                  bottom: 16,
                                  child: AnimatedBuilder(
                                    animation: _bounceAnimation,
                                    builder: (context, child) {
                                      return Transform.translate(
                                        offset: Offset(0, _bounceAnimation.value),
                                        child: Container(
                                          width: 60,
                                          height: 40,
                                          decoration: BoxDecoration(
                                            color: Colors.transparent,
                                            borderRadius: BorderRadius.circular(20),
                                            boxShadow: [
                                              BoxShadow(
                                                color: Colors.black.withOpacity(0.1),
                                                blurRadius: 8,
                                                offset: const Offset(0, 2),
                                              ),
                                            ],
                                          ),
                                          child: Padding(
                                            padding: const EdgeInsets.all(8.0),
                                            child: Image.asset(
                                              AppAssets.handbounce,
                                              width: 60,
                                              height: 30,
                                              fit: BoxFit.contain,
                                            ),
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                ),
                              ],
                            ),
                            const Gap(32),
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 26),
                              child: SizedBox(
                                width: double.infinity,
                                child: PrimaryButton(
                                  onPressed: () {
                                    context.pushRoute(
                                        const NotificationAllowRoute());
                                    // mixpanel
                                    sl<MixpanelService>().trackButtonClick(
                                        'Remind Me to Continue Later',
                                        properties: {
                                          'Page': 'Welcome Feed Page',
                                          'Code':
                                              'click.welcome_feed_page.remind_me_to_continue_later'
                                        });
                                  },
                                  text: 'Remind me to continue later',
                                ),
                              ),
                            ),
                            const Gap(64),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        } else if (state is WelcomeFeedLoading) {
          return const LoadingWidget(color: Colors.white);
        } else {
          return RetryWidget(
            onRetry: () {
              context.read<WelcomeFeedCubit>().getExercises();
            },
            color: Colors.white,
          );
        }
      },
    );
  }
}
